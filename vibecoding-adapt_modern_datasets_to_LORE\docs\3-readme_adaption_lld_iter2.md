# LORE-TSR项目TableLabelMe数据格式支持详细设计（迭代2）

## 项目结构与总体设计

### 设计目标
在迭代1基础格式解析器的基础上，实现完整TableLabelMe数据集的目录扫描和文件索引构建功能，替换固定测试数据，支持真实的大规模数据集处理。

### 核心设计原则
- **简约至上**：选择满足当前需求的最简单方案
- **无侵害性**：对原项目核心逻辑零修改
- **接口一致性**：保持与迭代1设计的完全兼容
- **性能优先**：支持10万+文件规模的高效处理

## 目录结构树 (Directory Tree)

```
LORE-TSR/src/lib/datasets/
├── dataset_factory.py                    # [现有] TableLabelMe映射
├── dataset/
│   ├── table.py                         # [现有] COCO格式标准数据集
│   ├── table_mid.py                     # [现有] COCO格式中等尺寸数据集
│   ├── table_small.py                   # [现有] COCO格式小尺寸数据集
│   └── table_labelmev2.py               # [修改] 实现完整_build_file_index方法
├── sample/
│   └── ctdet.py                         # [现有] CenterNet采样逻辑，无需修改
├── parsers/                             # [现有] 格式解析器模块
│   ├── __init__.py                      # [现有] 解析器包初始化
│   ├── base_parser.py                   # [现有] 解析器基类
│   └── tablelabelme_parser.py           # [现有] TableLabelMe格式解析器
└── scanners/                            # [新增] 目录扫描器模块
    ├── __init__.py                      # [新增] 扫描器包初始化
    ├── base_scanner.py                  # [新增] 扫描器基类（为后续扩展）
    ├── tablelabelme_scanner.py          # [新增] TableLabelMe目录扫描器
    └── file_matcher.py                  # [新增] 文件匹配工具

LORE-TSR/src/lib/
├── opts.py                              # [修改] 完善data_config参数处理
└── configs/                             # [扩展] 配置文件目录
    ├── __init__.py                      # [新增] 配置包初始化
    ├── dataset_configs.py               # [新增] 数据集配置模板
    └── config_loader.py                 # [新增] 配置加载器
```

## 整体逻辑和交互时序图

### 核心工作流程
迭代2在保持迭代1数据加载流程不变的基础上，实现真实数据集的目录扫描和索引构建。

```mermaid
sequenceDiagram
    participant Main as main.py
    participant DF as dataset_factory.py
    participant TLD as TableLabelMeDataset
    participant CL as ConfigLoader
    participant TLS as TableLabelMeScanner
    participant FM as FileMatcher
    participant TLP as TableLabelMeParser
    participant CTD as CTDetDataset

    Main->>DF: get_dataset('table', 'ctdet_mid')
    DF->>TLD: 创建TableLabelMeDataset类
    DF-->>Main: 返回组合Dataset类

    Main->>TLD: 初始化数据集
    TLD->>CL: load_config(data_config_path)
    CL-->>TLD: 配置字典
    TLD->>TLS: scan_directories(data_roots)
    TLS->>TLS: 递归扫描part_xxxx目录
    TLS->>FM: match_files(image_files, annotation_files)
    FM->>FM: 文件名匹配和ID生成
    FM-->>TLS: 文件配对列表
    TLS-->>TLD: 完整文件索引字典

    Main->>TLD: dataset[index]
    TLD->>TLD: 根据索引获取文件路径
    TLD->>TLP: parse_file(json_path, image_path)
    TLP-->>TLD: 标准化数据字典
    TLD->>CTD: __getitem__(标准化数据)
    CTD-->>TLD: 训练样本字典
    TLD-->>Main: 与COCO格式兼容的数据
```

## 数据实体结构深化

### 文件索引结构
```python
{
  "file_index": {
    12345: {                             # image_id (基于路径哈希)
      "image_path": "/path/to/image.jpg",
      "annotation_path": "/path/to/annotation.json",
      "part_name": "part_0001",
      "relative_path": "part_0001/image.jpg"
    }
  },
  "statistics": {
    "total_parts": 50,
    "total_images": 10000,
    "total_annotations": 9800,
    "matched_pairs": 9800,
    "unmatched_images": 200,
    "unmatched_annotations": 0,
    "scan_time": 15.6
  },
  "scan_config": {
    "data_roots": ["/path/to/dataset1", "/path/to/dataset2"],
    "part_pattern": "part_*",
    "image_extensions": [".jpg", ".png"],
    "annotation_patterns": [".json", "_table_annotation.json"]
  }
}
```

### 配置文件结构
```python
TABLELABELME_CONFIG = {
    "data_roots": [
        "/path/to/dataset1",
        "/path/to/dataset2"
    ],
    "scan_options": {
        "recursive": True,
        "part_pattern": "part_*",
        "image_extensions": [".jpg", ".png"],
        "annotation_patterns": [".json", "_table_annotation.json"],
        "max_depth": 3,
        "enable_cache": True,
        "cache_ttl": 3600
    },
    "quality_filter": {                  # 迭代3占位
        "enabled": False,
        "accepted_qualities": ["合格"]
    },
    "performance": {
        "batch_size": 1000,
        "use_multiprocessing": False,
        "max_workers": 4
    }
}
```

### 数据实体关系图
```mermaid
erDiagram
    DatasetConfig {
        list data_roots
        dict scan_options
        dict quality_filter
        dict performance
    }
    
    FileIndex {
        int image_id
        string image_path
        string annotation_path
        string part_name
        string relative_path
    }
    
    ScanStatistics {
        int total_parts
        int total_images
        int total_annotations
        int matched_pairs
        int unmatched_images
        int unmatched_annotations
        float scan_time
    }
    
    ParsedAnnotation {
        int image_id
        int annotation_id
        int category_id
        array segmentation
        array logic_axis
        float area
        array bbox
        dict extra_info
    }
    
    DatasetConfig ||--o{ FileIndex : "配置扫描生成"
    FileIndex ||--|| ScanStatistics : "统计信息"
    FileIndex ||--|| ParsedAnnotation : "解析转换"
```

## 配置项

### 命令行参数（保持兼容）
```bash
# TableLabelMe格式训练命令（迭代2完整版）
python main.py ctdet_mid \
  --dataset table \
  --dataset_name TableLabelMe \
  --data_config /path/to/dataset_configs.py \
  --arch resfpnhalf_18 \
  --batch_size 64 \
  --num_epochs 200
```

### 新增配置参数处理
- `--data_config`: 外部配置文件路径（迭代2实现完整功能）
- 配置文件缺失时的默认行为：使用内置测试数据路径
- 多数据源支持：配置文件中可指定多个数据根目录

### 环境变量支持（可选）
```bash
export TABLELABELME_DATA_ROOT="/default/path/to/dataset"
export TABLELABELME_CACHE_DIR="/tmp/tablelabelme_cache"
export TABLELABELME_LOG_LEVEL="INFO"
```

## 模块化文件详解 (File-by-File Breakdown)

### src/lib/datasets/scanners/__init__.py
**a. 文件用途说明**
扫描器包的初始化文件，提供统一的扫描器接口导入。

**b. 文件内容**
```python
from .base_scanner import BaseScanner
from .tablelabelme_scanner import TableLabelMeScanner
from .file_matcher import FileMatcher

__all__ = ['BaseScanner', 'TableLabelMeScanner', 'FileMatcher']
```

### src/lib/datasets/scanners/base_scanner.py
**a. 文件用途说明**
定义扫描器基类，为后续支持其他数据格式提供统一接口，预留质量筛选和错误处理扩展点。

**b. 文件内类图**
```mermaid
classDiagram
    class BaseScanner {
        +scan_directories(root_paths) dict
        +validate_structure(path) bool
        +generate_statistics() dict
        +get_scan_config() dict
        +set_scan_config(config) void
        #_scan_single_directory(path) list
        #_validate_file_pair(image_path, annotation_path) bool
        #_generate_cache_key(path) str
    }
```

**c. 函数/方法详解**

#### scan_directories方法
- **用途**: 扫描多个根目录，构建完整的文件索引
- **输入参数**:
  - `root_paths`: List[str] - 数据根目录路径列表
- **输出数据结构**: Dict - 包含file_index、statistics、scan_config的完整字典
- **实现流程**:
```mermaid
flowchart TD
    A[接收根目录列表] --> B[初始化扫描统计]
    B --> C{遍历每个根目录}
    C --> D[验证目录存在性]
    D --> E{目录有效?}
    E -->|否| F[记录警告，跳过]
    E -->|是| G[调用子类扫描方法]
    G --> H[合并扫描结果]
    H --> I{还有目录?}
    I -->|是| C
    I -->|否| J[生成统计信息]
    J --> K[返回完整索引]
    F --> I
```

#### validate_structure方法
- **用途**: 验证目录结构是否符合预期格式
- **输入参数**: `path` - 目录路径字符串
- **输出数据结构**: bool - 验证结果
- **实现流程**: 检查目录存在性和基本结构合理性

#### generate_statistics方法
- **用途**: 生成扫描过程的详细统计信息
- **输入参数**: 无（使用实例内部状态）
- **输出数据结构**: Dict - 统计信息字典
- **实现流程**: 汇总文件数量、匹配情况、扫描时间等信息

### src/lib/datasets/scanners/tablelabelme_scanner.py
**a. 文件用途说明**
TableLabelMe格式的具体扫描器实现，负责识别part_xxxx目录模式和文件收集。

**b. 文件内类图**
```mermaid
classDiagram
    BaseScanner <|-- TableLabelMeScanner
    class TableLabelMeScanner {
        +scan_directories(root_paths) dict
        +_scan_single_directory(path) list
        +_find_part_directories(root_path) list
        +_collect_files_in_part(part_path) tuple
        +_is_valid_part_directory(path) bool
        +_get_image_files(directory) list
        +_get_annotation_files(directory) list
    }
```

**c. 函数/方法详解**

#### _find_part_directories方法
- **用途**: 在根目录下查找所有符合part_xxxx模式的子目录
- **输入参数**: `root_path` - 根目录路径
- **输出数据结构**: List[str] - part目录路径列表
- **实现流程**:
```mermaid
sequenceDiagram
    participant Scanner as TableLabelMeScanner
    participant FS as FileSystem
    participant Pattern as PatternMatcher

    Scanner->>FS: listdir(root_path)
    FS-->>Scanner: 目录列表

    loop 每个目录
        Scanner->>Pattern: match("part_*", dir_name)
        Pattern-->>Scanner: 匹配结果
        alt 匹配成功
            Scanner->>FS: isdir(full_path)
            FS-->>Scanner: 是否为目录
            alt 是目录
                Scanner->>Scanner: 添加到part_dirs列表
            end
        end
    end

    Scanner-->>Scanner: 返回part_dirs列表
```

#### _collect_files_in_part方法
- **用途**: 收集单个part目录下的所有图像和标注文件
- **输入参数**: `part_path` - part目录路径
- **输出数据结构**: Tuple[List[str], List[str]] - (图像文件列表, 标注文件列表)
- **实现流程**: 递归扫描目录，按扩展名分类收集文件

### src/lib/datasets/scanners/file_matcher.py
**a. 文件用途说明**
文件匹配工具，负责图像和标注文件的配对，以及稳定image_id的生成。

**b. 文件内类图**
```mermaid
classDiagram
    class FileMatcher {
        +match_files(image_files, annotation_files) list
        +generate_image_id(file_path) int
        +_extract_base_name(file_path) str
        +_find_matching_annotation(image_path, annotation_files) str
        +_is_annotation_match(image_base, annotation_path) bool
        +_validate_file_pair(image_path, annotation_path) bool
    }
```

**c. 函数/方法详解**

#### match_files方法
- **用途**: 将图像文件和标注文件进行配对匹配
- **输入参数**:
  - `image_files`: List[str] - 图像文件路径列表
  - `annotation_files`: List[str] - 标注文件路径列表
- **输出数据结构**: List[Dict] - 匹配结果列表，每个元素包含image_id、image_path、annotation_path
- **实现流程**:
```mermaid
flowchart TD
    A[接收文件列表] --> B[初始化匹配结果]
    B --> C{遍历每个图像文件}
    C --> D[提取图像基础名称]
    D --> E[在标注文件中查找匹配]
    E --> F{找到匹配?}
    F -->|是| G[验证文件对有效性]
    F -->|否| H[记录未匹配图像]
    G --> I{验证通过?}
    I -->|是| J[生成image_id]
    I -->|否| H
    J --> K[添加到匹配结果]
    K --> L{还有图像?}
    H --> L
    L -->|是| C
    L -->|否| M[返回匹配结果]
```

#### generate_image_id方法
- **用途**: 基于文件路径生成稳定的唯一image_id
- **输入参数**: `file_path` - 文件路径字符串
- **输出数据结构**: int - 唯一的image_id
- **实现流程**: 使用MD5哈希算法确保ID的稳定性和唯一性

#### _find_matching_annotation方法
- **用途**: 为给定图像文件查找对应的标注文件
- **输入参数**:
  - `image_path` - 图像文件路径
  - `annotation_files` - 标注文件列表
- **输出数据结构**: str或None - 匹配的标注文件路径
- **实现流程**: 支持两种标注文件命名模式：`.json`和`_table_annotation.json`

### src/lib/datasets/dataset/table_labelmev2.py（修改部分）
**a. 文件用途说明**
扩展现有TableLabelMe数据集类，实现完整的`_build_file_index`方法，集成目录扫描功能。

**b. 主要修改内容**

#### _build_file_index方法（完整实现）
- **用途**: 构建完整的文件索引，替换迭代1的固定测试数据
- **输入参数**: 无（使用实例配置）
- **输出数据结构**: Dict - 完整的文件索引字典
- **实现流程**:
```mermaid
sequenceDiagram
    participant Dataset as TableLabelMeDataset
    participant Config as ConfigLoader
    participant Scanner as TableLabelMeScanner
    participant Cache as CacheManager

    Dataset->>Config: load_config(self.opt.data_config)
    Config-->>Dataset: 配置字典

    Dataset->>Cache: check_cache(config_hash)
    Cache-->>Dataset: 缓存状态

    alt 缓存有效
        Dataset->>Cache: load_from_cache()
        Cache-->>Dataset: 文件索引
    else 缓存无效或不存在
        Dataset->>Scanner: scan_directories(data_roots)
        Scanner-->>Dataset: 完整文件索引
        Dataset->>Cache: save_to_cache(file_index)
    end

    Dataset->>Dataset: 验证索引完整性
    Dataset-->>Dataset: 返回文件索引
```

#### __init__方法（扩展）
- **用途**: 初始化数据集，集成配置加载和扫描功能
- **输入参数**:
  - `opt`: 配置对象
  - `split`: 数据集分割（train/val/test）
- **输出数据结构**: 无（构造函数）
- **实现流程**: 加载配置 → 构建索引 → 初始化数据结构

### src/lib/configs/__init__.py
**a. 文件用途说明**
配置包的初始化文件，提供配置相关功能的统一导入。

**b. 文件内容**
```python
from .config_loader import ConfigLoader
from .dataset_configs import TABLELABELME_CONFIG, DEFAULT_CONFIG

__all__ = ['ConfigLoader', 'TABLELABELME_CONFIG', 'DEFAULT_CONFIG']
```

### src/lib/configs/config_loader.py
**a. 文件用途说明**
配置加载器，负责解析和验证外部配置文件，提供配置缓存和默认值处理。

**b. 文件内类图**
```mermaid
classDiagram
    class ConfigLoader {
        +load_config(config_path) dict
        +validate_config(config) bool
        +merge_with_defaults(config) dict
        +get_default_config() dict
        +_parse_config_file(path) dict
        +_validate_paths(config) bool
        +_expand_environment_variables(config) dict
    }
```

**c. 函数/方法详解**

#### load_config方法
- **用途**: 加载并验证外部配置文件
- **输入参数**: `config_path` - 配置文件路径（可为空）
- **输出数据结构**: Dict - 验证后的配置字典
- **实现流程**:
```mermaid
flowchart TD
    A[接收配置路径] --> B{路径为空?}
    B -->|是| C[使用默认配置]
    B -->|否| D[检查文件存在性]
    D --> E{文件存在?}
    E -->|否| F[记录警告，使用默认配置]
    E -->|是| G[解析配置文件]
    G --> H[验证配置格式]
    H --> I{验证通过?}
    I -->|否| J[记录错误，使用默认配置]
    I -->|是| K[与默认配置合并]
    K --> L[展开环境变量]
    L --> M[返回最终配置]
    C --> M
    F --> M
    J --> M
```

#### validate_config方法
- **用途**: 验证配置文件的格式和内容正确性
- **输入参数**: `config` - 配置字典
- **输出数据结构**: bool - 验证结果
- **实现流程**: 检查必需字段、数据类型、路径有效性等

### src/lib/configs/dataset_configs.py
**a. 文件用途说明**
数据集配置模板和默认配置定义，为用户提供配置参考。

**b. 配置模板内容**
```python
# TableLabelMe数据集配置模板
TABLELABELME_CONFIG = {
    "data_roots": [
        # 在此添加您的数据集根目录路径
        # "/path/to/your/tablelabelme/dataset1",
        # "/path/to/your/tablelabelme/dataset2"
    ],
    "scan_options": {
        "recursive": True,
        "part_pattern": "part_*",
        "image_extensions": [".jpg", ".png", ".jpeg"],
        "annotation_patterns": [".json", "_table_annotation.json"],
        "max_depth": 3,
        "enable_cache": True,
        "cache_ttl": 3600,
        "ignore_hidden": True
    },
    "quality_filter": {                  # 迭代3实现
        "enabled": False,
        "accepted_qualities": ["合格"],
        "strict_mode": False
    },
    "performance": {
        "batch_size": 1000,
        "use_multiprocessing": False,
        "max_workers": 4,
        "memory_limit": "2GB"
    },
    "logging": {
        "level": "INFO",
        "enable_progress": True,
        "log_file": None
    }
}

# 默认配置（用于测试和开发）
DEFAULT_CONFIG = {
    "data_roots": ["./test_data/tablelabelme"],
    "scan_options": {
        "recursive": True,
        "part_pattern": "part_*",
        "image_extensions": [".jpg", ".png"],
        "annotation_patterns": [".json"],
        "max_depth": 2,
        "enable_cache": False,
        "cache_ttl": 0,
        "ignore_hidden": True
    },
    "quality_filter": {
        "enabled": False,
        "accepted_qualities": ["合格"],
        "strict_mode": False
    },
    "performance": {
        "batch_size": 100,
        "use_multiprocessing": False,
        "max_workers": 1,
        "memory_limit": "1GB"
    },
    "logging": {
        "level": "DEBUG",
        "enable_progress": True,
        "log_file": None
    }
}
```

### src/lib/opts.py（修改部分）
**a. 文件用途说明**
扩展现有配置参数解析，完善data_config参数的处理逻辑。

**b. 主要修改内容**
```python
# 在现有代码基础上修改
def __init__(self):
    # ... 现有代码 ...

    # 扩展data_config参数处理
    self.parser.add_argument('--data_config', default='',
                           help='path to external dataset configuration file for TableLabelMe format')

    # 添加配置验证参数
    self.parser.add_argument('--validate_config', action='store_true',
                           help='validate configuration file and exit')

def parse(self, args=''):
    # ... 现有解析逻辑 ...

    # 处理data_config参数
    if opt.data_config and not os.path.exists(opt.data_config):
        print(f'Warning: Configuration file {opt.data_config} not found, using default settings')
        opt.data_config = ''

    # 配置验证模式
    if opt.validate_config:
        from .configs.config_loader import ConfigLoader
        loader = ConfigLoader()
        config = loader.load_config(opt.data_config)
        print("Configuration validation passed!")
        print(f"Loaded configuration: {config}")
        exit(0)

    return opt
```

## 迭代演进依据

### 架构扩展性设计
1. **扫描器扩展**: `BaseScanner`基类支持未来其他数据格式的扫描器实现
2. **配置系统扩展**: 完整的配置加载和验证机制，支持复杂配置需求
3. **缓存机制扩展**: 文件索引缓存支持，提升大数据集处理性能
4. **性能优化扩展**: 预留多进程和批量处理接口

### 后续迭代占位
- **迭代3**: 质量筛选和错误处理功能在`quality_filter`配置中占位，`BaseScanner`预留质量筛选接口
- **迭代4**: 配置系统集成已在迭代2完整实现，为迭代4奠定基础
- **迭代5**: 训练流程集成通过保持接口兼容性支持
- **迭代6**: 可视化工具通过文件索引接口获取数据访问能力

### 技术债务控制
- 每个文件控制在500行以内，模块功能相对完整且独立
- 避免过度细化，保持适度的抽象层次
- 清晰的接口定义和扩展点预留
- 完善的错误处理和日志记录机制

### 性能优化策略
1. **内存管理**: 使用生成器模式处理大数据集，避免内存占用过大
2. **缓存机制**: 文件索引缓存，避免重复扫描，支持增量更新
3. **并行处理**: 预留多进程扫描接口，支持大规模数据集快速处理
4. **延迟加载**: 文件内容按需加载，减少初始化时间

## 如何迁移现有COCO格式功能

### 代码文件对应关系
| 现有COCO格式文件 | TableLabelMe对应文件 | 迁移策略 | 迭代2变更 |
|-----------------|-------------------|---------|----------|
| `table.py` | `table_labelmev2.py` | 继承基础功能，重写数据加载逻辑 | 实现完整_build_file_index |
| `ctdet.py` | 无需修改 | 直接复用现有采样和预处理逻辑 | 无变更 |
| `dataset_factory.py` | 扩展映射 | 添加新的数据集类型映射 | 无变更 |
| `opts.py` | 扩展参数 | 添加data_config参数支持 | 完善参数处理逻辑 |

### 兼容性保证
- 所有现有COCO格式功能保持不变
- 新增功能通过参数组合区分，不影响原有流程
- Dataset.__getitem__返回结构完全兼容，确保下游模块无需修改
- 配置系统向后兼容，支持原有参数组合

### 迁移验证策略
1. **功能验证**: 确保所有现有COCO格式测试用例通过
2. **性能验证**: 对比迭代1和迭代2的数据加载性能
3. **兼容性验证**: 验证原有训练脚本无需修改即可运行
4. **扩展性验证**: 验证新增扫描功能在大数据集上的表现

### 数据格式对比
| 特性 | COCO格式 | TableLabelMe格式 | 迭代2支持 |
|------|----------|-----------------|----------|
| 数据组织 | 单一标注文件 | 分布式文件结构 | ✅ 完整支持 |
| 目录扫描 | 不需要 | part_xxxx模式 | ✅ 自动识别 |
| 文件匹配 | 不需要 | 图像-标注配对 | ✅ 智能匹配 |
| 配置复杂度 | 简单 | 中等 | ✅ 配置模板 |
| 扩展性 | 有限 | 良好 | ✅ 模块化设计 |

---

**文档版本**: v2.0
**创建日期**: 2025年1月21日
**迭代范围**: 迭代2 - 目录扫描和文件索引构建
**前置依赖**: 迭代1 - 基础格式解析器实现
**后续迭代**: 通过配置占位和接口预留，保持架构完整性
